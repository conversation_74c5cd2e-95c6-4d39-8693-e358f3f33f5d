"""
Streamlined A.T.L.A.S Trading System - Main Server
Consolidated FastAPI server with all trading capabilities
"""

import asyncio
import logging
import logging.config
from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, HTTPException, Query, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel

# Import streamlined modules
from .config import settings, LOGGING_CONFIG
from .models import (
    Quote, OHLCV, ScanResult, ChatMessage, AIResponse,
    OrderRequest, PortfolioMetrics, Position, EducationQuery
)
from .market_data import MarketDataService
from .technical_analysis import TechnicalAnalysisEngine, TechnicalScanner
from .ai_services import AIServices
from .trading_engine import TradingEngine
from .trading_books_rag import TradingEducationRAG
from .feedback_system import FeedbackSystem
from .validation_engine import TradingValidationEngine
from .cot_trading_orchestrator import ChainOfThoughtTradingOrchestrator
from .conversational_cot_interface import ConversationalCoTInterface

# Configure logging
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="A.T.L.A.S AI Trading System - Streamlined",
    description="Advanced Trading & Learning Analysis System with ChatGPT-like AI",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class ChatRequest(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = None

class ScanRequest(BaseModel):
    scan_type: str
    symbols: Optional[List[str]] = None
    limit: int = 10

class QuoteRequest(BaseModel):
    symbol: str

class HistoricalRequest(BaseModel):
    symbol: str
    timeframe: str = "1Day"
    limit: int = 100

class FeedbackRequest(BaseModel):
    message_id: str
    feedback_type: str
    rating: Optional[int] = None
    comment: str = ""
    improvement_suggestion: str = ""

class PreferencesRequest(BaseModel):
    max_risk_per_trade: Optional[float] = None
    preferred_strategies: Optional[List[str]] = None
    min_volume_ratio: Optional[float] = None

# Global service instances
market_data_service = MarketDataService()
technical_scanner = TechnicalScanner()
ai_services = AIServices()
trading_engine = TradingEngine()
education_rag = TradingEducationRAG()
cot_orchestrator = ChainOfThoughtTradingOrchestrator()
conversational_cot = ConversationalCoTInterface()

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    logger.info("🚀 Starting A.T.L.A.S AI Trading System - Streamlined")
    logger.info("🎯 Advanced Trading & Learning Analysis System")
    logger.info("📚 Educational Paper Trading Mode with RAG")

@app.get("/")
async def root():
    """Health check and system status"""
    return {
        "status": "A.T.L.A.S AI Server Running - Streamlined",
        "version": "2.0.0",
        "system": "Advanced Trading & Learning Analysis System",
        "description": "Streamlined ChatGPT-like Trading Assistant with RAG Education",
        "features": [
            "Real-time market data",
            "Technical analysis scanner", 
            "AI-enhanced stop-loss",
            "RAG trading education",
            "Event explanation engine",
            "Portfolio tracking",
            "Paper trading execution"
        ],
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/capabilities")
async def get_capabilities():
    """Get system capabilities"""
    return {
        "trading": {
            "paper_trading": settings.PAPER_TRADING,
            "real_time_data": True,
            "order_types": ["market", "limit", "stop", "bracket"],
            "risk_management": True
        },
        "analysis": {
            "technical_indicators": True,
            "pattern_recognition": True,
            "sentiment_analysis": True,
            "ai_stop_loss": True
        },
        "education": {
            "rag_system": True,
            "trading_books": education_rag.get_available_books(),
            "concept_explanations": True
        },
        "ai_features": {
            "conversational_interface": True,
            "market_explanations": True,
            "trading_plans": True,
            "educational_responses": True
        }
    }

# Core Feature 1: Live Quotes & Market Data
@app.get("/api/v1/quote/{symbol}")
async def get_quote(symbol: str):
    """Get real-time stock quote"""
    try:
        async with market_data_service:
            quote = await market_data_service.get_real_time_quote(symbol)
            return quote.dict()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/historical")
async def get_historical_data(request: HistoricalRequest):
    """Get historical OHLCV data for charting"""
    try:
        async with market_data_service:
            data = await market_data_service.get_historical_data(
                request.symbol, 
                request.timeframe, 
                request.limit
            )
            return [bar.dict() for bar in data]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Core Feature 2: Technical Analysis Scanner
@app.post("/api/v1/scan")
async def run_technical_scan(request: ScanRequest):
    """Run technical analysis scan"""
    try:
        if request.scan_type == "oversold":
            results = await technical_scanner.scan_oversold_stocks(request.symbols)
        elif request.scan_type == "breakout":
            results = await technical_scanner.scan_breakout_patterns(request.symbols)
        elif request.scan_type == "ttm_squeeze":
            results = await technical_scanner.scan_ttm_squeeze(request.symbols)
        elif request.scan_type == "macd_bullish":
            results = await technical_scanner.scan_macd_bullish(request.symbols)
        elif request.scan_type == "comprehensive":
            results = await technical_scanner.run_comprehensive_scan()
            return results  # Already a dict
        else:
            raise HTTPException(status_code=400, detail="Invalid scan type")
        
        return [result.dict() for result in results[:request.limit]]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Core Feature 3: LLM Q&A Integration
@app.post("/api/v1/chat")
async def chat_with_atlas(request: ChatRequest):
    """Main A.T.L.A.S chat interface"""
    try:
        response = await ai_services.process_chat_message(
            request.message,
            request.context
        )
        return response.dict()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/chat/cot")
async def chat_with_chain_of_thought(request: ChatRequest):
    """Enhanced conversational interface with Chain-of-Thought trading intelligence"""
    try:
        response = await conversational_cot.process_user_message(
            request.message,
            request.context
        )
        return {
            "response": response.response,
            "type": response.type,
            "requires_action": response.requires_action,
            "trading_plan": response.trading_plan,
            "confidence": response.confidence,
            "timestamp": response.timestamp.isoformat()
        }
    except Exception as e:
        logger.error(f"Error in CoT chat: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Core Feature 4: Order Routing (Paper Trading)
@app.post("/api/v1/orders")
async def place_order(order_request: OrderRequest):
    """Place trading order"""
    try:
        order_id = await trading_engine.place_order(order_request)
        if order_id:
            return {"order_id": order_id, "status": "submitted"}
        else:
            raise HTTPException(status_code=400, detail="Failed to place order")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/orders/bracket")
async def place_bracket_order(
    symbol: str, 
    qty: float, 
    entry_price: float, 
    target_price: float, 
    stop_price: float
):
    """Place bracket order with entry, target, and stop"""
    try:
        result = await trading_engine.place_bracket_order(
            symbol, qty, entry_price, target_price, stop_price
        )
        if result:
            return result
        else:
            raise HTTPException(status_code=400, detail="Failed to place bracket order")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Core Feature 5: Portfolio Tracking
@app.get("/api/v1/portfolio")
async def get_portfolio():
    """Get portfolio metrics and positions"""
    try:
        return await trading_engine.get_account_summary()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/positions")
async def get_positions():
    """Get current positions"""
    try:
        positions = await trading_engine.get_positions()
        return [pos.dict() for pos in positions]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Core Feature 6: Event Explanation Engine
@app.get("/api/v1/explain/{symbol}")
async def explain_price_movement(symbol: str):
    """Explain recent price movement"""
    try:
        async with market_data_service:
            explanation = await market_data_service.explain_price_movement(symbol)
            return explanation
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/explain/market")
async def explain_market_event(question: str):
    """Explain market events using AI"""
    try:
        response = await ai_services.process_chat_message(
            f"Explain this market event: {question}",
            context={"type": "market_explanation"}
        )
        return {"explanation": response.response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Core Feature 7: Teaching Mode (RAG Education)
@app.post("/api/v1/education/query")
async def education_query(query: EducationQuery):
    """Answer educational query using RAG system"""
    try:
        response = await education_rag.answer_education_query(query)
        return response.dict()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/education/concept/{concept}")
async def explain_concept(concept: str):
    """Explain trading concept"""
    try:
        explanation = await education_rag.explain_trading_concept(concept)
        return {"concept": concept, "explanation": explanation}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/education/books")
async def get_available_books():
    """Get available trading books"""
    try:
        books = education_rag.get_available_books()
        return {"books": books}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/education/book-wisdom")
async def get_book_wisdom(question: str, book_title: str):
    """Get wisdom from specific trading book"""
    try:
        wisdom = await education_rag.get_book_wisdom(question, book_title)
        return {"question": question, "book": book_title, "wisdom": wisdom}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Additional AI Features
@app.post("/api/v1/ai/stop-loss")
async def calculate_ai_stop_loss(
    symbol: str, 
    entry_price: float, 
    direction: str, 
    risk_percent: float = 2.0
):
    """Calculate AI-enhanced stop loss"""
    try:
        result = await ai_services.calculate_ai_stop_loss(
            symbol, entry_price, direction, risk_percent
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/ai/trading-plan")
async def create_trading_plan(goal: str, symbols: Optional[List[str]] = None):
    """Create AI-generated trading plan"""
    try:
        plan = await trading_engine.create_ai_trading_plan(goal, symbols)
        return plan
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/news/{symbol}")
async def get_market_news(symbol: str, limit: int = 10):
    """Get market news for symbol"""
    try:
        async with market_data_service:
            news = await market_data_service.get_market_news(symbol, limit)
            return [article.dict() for article in news]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Enhanced Predictions API with Predicto Integration
@app.get("/api/v1/predictions/{symbol}")
async def get_market_predictions(symbol: str):
    """Get AI-enhanced market predictions using Predicto deep learning models"""
    try:
        async with market_data_service:
            # Get enhanced market context with Predicto predictions
            context = await market_data_service.get_enhanced_market_context(symbol)

            if "error" in context:
                raise HTTPException(status_code=400, detail=context["error"])

            # Extract Predicto-specific data
            predictions = {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "predicto_enabled": context.get("predicto_enabled", False),
                "forecast": context.get("predicto_forecast"),
                "nasdaq_outlook": context.get("predicto_nasdaq_outlook"),
                "market_sentiment": context.get("predicto_market_sentiment"),
                "ai_recommendation": context.get("predicto_ai_recommendation"),
                "confidence_level": context.get("predicto_confidence"),
                "technical_context": {
                    "current_price": context.get("current_price"),
                    "price_change_percent": context.get("price_change_percent"),
                    "rsi": context.get("rsi"),
                    "trend_direction": context.get("trend_direction"),
                    "momentum_direction": context.get("momentum_direction"),
                    "ttm_squeeze": context.get("ttm_squeeze")
                }
            }

            return predictions
    except Exception as e:
        logger.error(f"Error getting predictions for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/predictions/nasdaq/outlook")
async def get_nasdaq_outlook():
    """Get Nasdaq market outlook and sentiment analysis"""
    try:
        async with market_data_service:
            # Get Nasdaq outlook from Predicto
            outlook = await market_data_service.predicto.get_nasdaq_outlook(days_back=7)

            return {
                "timestamp": datetime.now().isoformat(),
                "outlook_data": [o.__dict__ for o in outlook],
                "summary": {
                    "avg_outlook_score": sum(o.outlook_score for o in outlook) / len(outlook) if outlook else 0,
                    "avg_volatility": sum(o.forecasted_volatility for o in outlook) / len(outlook) if outlook else 0,
                    "avg_uncertainty": sum(o.models_uncertainty for o in outlook) / len(outlook) if outlook else 0,
                    "market_sentiment": market_data_service.predicto._analyze_market_sentiment(outlook)
                }
            }
    except Exception as e:
        logger.error(f"Error getting Nasdaq outlook: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/predictions/supported-tickers")
async def get_supported_prediction_tickers():
    """Get list of tickers supported by Predicto predictions"""
    try:
        async with market_data_service:
            tickers = await market_data_service.predicto.get_supported_tickers()
            return {
                "supported_tickers": tickers,
                "count": len(tickers),
                "predicto_enabled": market_data_service.predicto.enabled
            }
    except Exception as e:
        logger.error(f"Error getting supported tickers: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Utility endpoints
@app.get("/api/v1/conversation/summary")
async def get_conversation_summary():
    """Get conversation summary"""
    try:
        summary = ai_services.get_conversation_summary()
        return {"summary": summary}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/conversation/clear")
async def clear_conversation():
    """Clear conversation history"""
    try:
        ai_services.clear_conversation_history()
        return {"status": "conversation cleared"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Enhanced Features - Feedback and Personalization
@app.post("/api/v1/feedback")
async def collect_feedback(feedback: FeedbackRequest):
    """Collect user feedback on AI responses"""
    try:
        success = await ai_services.feedback_system.collect_feedback(
            feedback.message_id,
            feedback.feedback_type,
            feedback.rating,
            feedback.comment,
            feedback.improvement_suggestion
        )

        if success:
            return {"status": "feedback collected", "message_id": feedback.message_id}
        else:
            raise HTTPException(status_code=400, detail="Failed to collect feedback")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/feedback/summary")
async def get_feedback_summary(days: int = 30):
    """Get feedback summary"""
    try:
        summary = await ai_services.feedback_system.get_feedback_summary(days)
        return summary
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/preferences")
async def update_preferences(preferences: PreferencesRequest):
    """Update user trading preferences"""
    try:
        # Convert to dict and filter None values
        prefs_dict = {k: v for k, v in preferences.dict().items() if v is not None}

        success = ai_services.trading_rules.update_user_preferences(prefs_dict)

        if success:
            return {"status": "preferences updated", "preferences": prefs_dict}
        else:
            raise HTTPException(status_code=400, detail="Failed to update preferences")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/preferences")
async def get_preferences():
    """Get current user preferences"""
    try:
        preferences = ai_services.trading_rules.get_user_preferences()
        return {"preferences": preferences}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/validation/summary")
async def get_validation_summary(days: int = 7):
    """Get validation summary"""
    try:
        summary = ai_services.validation_engine.get_validation_summary(days)
        return summary
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/trading-rules/performance")
async def get_trading_rules_performance():
    """Get trading rules performance statistics"""
    try:
        performance = ai_services.trading_rules.get_strategy_performance()
        return {"strategy_performance": performance}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Chain-of-Thought Trading Endpoints
@app.post("/api/v1/cot/create-plan")
async def create_cot_trading_plan(
    user_request: str = Body(..., description="User's trading request (e.g., 'Make me $200 today')"),
    account_size: float = Body(..., description="Account size in dollars"),
    risk_tolerance: str = Body(default="moderate", description="Risk tolerance: conservative, moderate, aggressive")
):
    """Create comprehensive trading plan with Chain-of-Thought analysis"""
    try:
        plan = await cot_orchestrator.create_comprehensive_trading_plan(
            user_request=user_request,
            account_size=account_size,
            risk_tolerance=risk_tolerance
        )
        return plan
    except Exception as e:
        logger.error(f"Error creating CoT trading plan: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/cot/analyze-symbol")
async def analyze_symbol_with_cot(
    symbol: str = Body(..., description="Stock symbol to analyze"),
    account_size: float = Body(..., description="Account size for position sizing"),
    confidence_override: Optional[float] = Body(default=None, description="Override confidence score (0.0-1.0)")
):
    """Analyze symbol with full Chain-of-Thought reasoning"""
    try:
        analysis = await cot_orchestrator.execute_trade_with_full_cot(
            symbol=symbol,
            account_size=account_size,
            confidence_override=confidence_override
        )
        return analysis
    except Exception as e:
        logger.error(f"Error analyzing {symbol} with CoT: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/cot/dashboard")
async def get_cot_dashboard(
    account_size: float = Query(..., description="Account size in dollars")
):
    """Get comprehensive Chain-of-Thought trading dashboard"""
    try:
        dashboard = await cot_orchestrator.get_portfolio_dashboard(account_size)
        return dashboard
    except Exception as e:
        logger.error(f"Error generating CoT dashboard: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Health check endpoint
@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "2.0.0",
        "services": {
            "market_data": "active",
            "ai_services": "active",
            "education_rag": "active",
            "chain_of_thought": "active"
        }
    }


# Serve static files (frontend) - check both possible locations
import os

# Check for frontend build in parent directory (correct location)
frontend_static_path = "../frontend/build/static"
frontend_index_path = "../frontend/build/index.html"

if os.path.exists(frontend_static_path):
    app.mount("/static", StaticFiles(directory=frontend_static_path), name="static")
    logger.info(f"Frontend static files mounted from {frontend_static_path}")

    # Serve the React app for all non-API routes
    @app.get("/{path:path}")
    async def serve_frontend(path: str):
        """Serve React frontend for all non-API routes"""
        if path.startswith("api/"):
            raise HTTPException(status_code=404, detail="API endpoint not found")

        # Serve the built React index.html
        if os.path.exists(frontend_index_path):
            return FileResponse(frontend_index_path)
        else:
            raise HTTPException(status_code=404, detail="Frontend not found")

else:
    # Fallback: check local directory
    local_static_path = "frontend/build/static"
    local_index_path = "frontend/build/index.html"

    if os.path.exists(local_static_path):
        app.mount("/static", StaticFiles(directory=local_static_path), name="static")
        logger.info(f"Frontend static files mounted from {local_static_path}")

        @app.get("/{path:path}")
        async def serve_frontend_local(path: str):
            """Serve React frontend for all non-API routes"""
            if path.startswith("api/"):
                raise HTTPException(status_code=404, detail="API endpoint not found")

            if os.path.exists(local_index_path):
                return FileResponse(local_index_path)
            else:
                raise HTTPException(status_code=404, detail="Frontend not found")
    else:
        logger.warning("Frontend build directory not found in any expected location")

        # Serve basic fallback for root route only
        @app.get("/")
        async def serve_fallback():
            """Serve basic HTML fallback when frontend not built"""
            return HTMLResponse("""
            <!DOCTYPE html>
            <html>
            <head>
                <title>A.T.L.A.S AI Trading System</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background: #0a0a0a; color: #00ffff; }
                    .container { max-width: 800px; margin: 0 auto; text-align: center; }
                    .logo { font-size: 3em; margin-bottom: 20px; }
                    .subtitle { font-size: 1.2em; margin-bottom: 30px; color: #888; }
                    .links { margin-top: 30px; }
                    .links a { color: #00ffff; text-decoration: none; margin: 0 20px; }
                    .links a:hover { text-decoration: underline; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="logo">🧠 A.T.L.A.S</div>
                    <div class="subtitle">Advanced Trading & Learning Analysis System</div>
                    <p>Chain-of-Thought Trading Intelligence Active</p>
                    <div class="links">
                        <a href="/docs">📚 API Documentation</a>
                        <a href="/api/v1/health">🔍 Health Check</a>
                    </div>
                    <p style="margin-top: 40px; color: #666;">
                        Frontend not built. Build the React frontend to see the full interface.
                    </p>
                </div>
            </body>
            </html>
            """)

if __name__ == "__main__":
    import uvicorn
    
    print("🚀 Starting Streamlined A.T.L.A.S AI Server...")
    print("🎯 Advanced Trading & Learning Analysis System")
    print("📚 Educational Paper Trading with RAG")
    
    uvicorn.run(
        "atlas_server:app",
        host="0.0.0.0",
        port=settings.PORT,
        reload=True,
        log_level=settings.LOG_LEVEL.lower()
    )
